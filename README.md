# 📚 校园二手书交易系统

一个基于 Vue 3 + TypeScript + Element Plus 的现代化校园二手书交易平台。

## ✨ 功能特性

### 🔐 用户系统
- 用户注册与登录
- 用户角色管理（普通用户/管理员）
- 个人信息管理

### 📖 图书管理
- 图书浏览与搜索
- 图书分类筛选
- 图书详情查看
- 图书发布与管理
- 图书状态管理（在售/已售出）

### 🛒 交易功能
- 一键购买图书
- 订单管理
- 交易记录查看

### 📱 响应式设计
- 支持桌面端和移动端
- 自适应布局
- 优雅的用户界面

## 🛠️ 技术栈

- **前端框架**: Vue 3 (Composition API)
- **开发语言**: TypeScript
- **构建工具**: Vite
- **UI 组件库**: Element Plus + Bootstrap
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **HTTP 客户端**: Axios
- **样式预处理**: CSS3

## 📁 项目结构

```
src/
├── api/                    # API 接口封装
│   └── index.ts
├── assets/                 # 静态资源
├── components/             # 公共组件
│   └── Layout/
│       └── AppHeader.vue
├── mock/                   # Mock 数据
│   └── data.ts
├── router/                 # 路由配置
│   └── index.ts
├── stores/                 # Pinia 状态管理
│   ├── index.ts
│   ├── user.ts
│   ├── book.ts
│   └── order.ts
├── types/                  # TypeScript 类型定义
│   └── index.ts
├── utils/                  # 工具函数
│   └── request.ts
├── views/                  # 页面组件
│   ├── Home.vue           # 首页
│   ├── Login.vue          # 登录页
│   ├── Register.vue       # 注册页
│   ├── BookDetail.vue     # 图书详情
│   ├── PublishBook.vue    # 发布图书
│   ├── MyBooks.vue        # 我的发布
│   ├── MyOrders.vue       # 我的订单
│   ├── Profile.vue        # 个人中心
│   └── NotFound.vue       # 404页面
├── App.vue                # 根组件
└── main.ts               # 应用入口
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 类型检查
```bash
npm run type-check
```

## 🎯 演示账户

### 管理员账户
- 用户名: `admin`
- 密码: `123456`

### 普通用户账户
- 用户名: `user1`
- 密码: `123456`

## 📋 主要页面

### 🏠 首页 (`/home`)
- 展示所有可售图书
- 支持按分类和关键词搜索
- 响应式图书卡片布局

### 🔐 登录/注册 (`/login`, `/register`)
- 用户身份验证
- 表单验证
- 自动跳转

### 📚 图书详情 (`/book/:id`)
- 详细图书信息
- 购买功能
- 卖家信息

### ✍️ 发布图书 (`/publish`)
- 图书信息录入
- 图片上传预览
- 表单验证

### 📖 我的发布 (`/my-books`)
- 个人发布的图书列表
- 销售状态管理

### 🛒 我的订单 (`/my-orders`)
- 购买记录查看
- 订单状态跟踪

### 👤 个人中心 (`/profile`)
- 用户信息展示
- 统计数据
- 快捷操作

## 🔧 开发说明

### Mock 数据
项目使用本地 Mock 数据模拟后端 API，数据存储在 `src/mock/data.ts` 中。

### 状态管理
使用 Pinia 进行状态管理，主要包括：
- `userStore`: 用户信息和认证状态
- `bookStore`: 图书数据和操作
- `orderStore`: 订单数据管理

### 路由守卫
实现了基于用户登录状态的路由守卫，保护需要认证的页面。

### 响应式设计
使用 CSS Grid 和 Flexbox 实现响应式布局，适配不同屏幕尺寸。

## 🎨 UI 设计

- 采用现代化的卡片式设计
- 统一的色彩方案和字体
- 流畅的动画效果
- 直观的用户交互

## 📱 移动端适配

- 响应式导航菜单
- 触摸友好的交互元素
- 优化的移动端布局
- 抽屉式侧边菜单

## 🔮 后续扩展

- [ ] 用户评价系统
- [ ] 图书收藏功能
- [ ] 在线聊天功能
- [ ] 支付集成
- [ ] 管理员后台
- [ ] 数据统计分析
- [ ] 推荐算法

## 📄 许可证

MIT License
