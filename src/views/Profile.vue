<template>
  <div class="profile-container">
    <div class="profile-card">
      <div class="profile-header">
        <div class="avatar-section">
          <el-avatar :size="80" :src="userStore.user?.avatar" />
        </div>
        <div class="user-info">
          <h2>{{ userStore.user?.username }}</h2>
          <p>{{ userStore.user?.email }}</p>
          <el-tag :type="userStore.user?.role === 'admin' ? 'danger' : 'primary'">
            {{ userStore.user?.role === 'admin' ? '管理员' : '普通用户' }}
          </el-tag>
        </div>
      </div>

      <div class="profile-stats">
        <div class="stat-item">
          <div class="stat-value">{{ myBooksCount }}</div>
          <div class="stat-label">发布图书</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ myOrdersCount }}</div>
          <div class="stat-label">购买订单</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ joinDays }}</div>
          <div class="stat-label">加入天数</div>
        </div>
      </div>

      <div class="profile-actions">
        <el-button type="primary" @click="$router.push('/my-books')">
          我的发布
        </el-button>
        <el-button @click="$router.push('/my-orders')">
          我的订单
        </el-button>
        <el-button @click="$router.push('/publish')">
          发布图书
        </el-button>
        <el-button type="danger" @click="handleLogout">
          退出登录
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { useUserStore, useBookStore, useOrderStore } from '@/stores'

const router = useRouter()
const userStore = useUserStore()
const bookStore = useBookStore()
const orderStore = useOrderStore()

// 计算属性
const myBooksCount = computed(() => bookStore.myBooks.length)
const myOrdersCount = computed(() => orderStore.orders.length)

const joinDays = computed(() => {
  if (!userStore.user?.createdAt) return 0
  const joinDate = new Date(userStore.user.createdAt)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - joinDate.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
})

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确认退出登录吗？',
      '退出登录',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    userStore.logout()
    router.push('/home')
  } catch (error) {
    // 用户取消
  }
}

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([
    bookStore.getMyBooks(),
    orderStore.getMyOrders()
  ])
})
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 40px 20px;
}

.profile-card {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid #e9ecef;
}

.avatar-section {
  flex-shrink: 0;
}

.user-info h2 {
  font-size: 1.5rem;
  color: #333;
  margin: 0 0 8px 0;
}

.user-info p {
  color: #666;
  margin: 0 0 10px 0;
}

.profile-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid #e9ecef;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

.profile-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

@media (max-width: 480px) {
  .profile-header {
    flex-direction: column;
    text-align: center;
  }
  
  .profile-stats {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .profile-actions {
    grid-template-columns: 1fr;
  }
}
</style>
