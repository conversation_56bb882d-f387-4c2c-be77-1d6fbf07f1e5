<template>
  <div class="publish-container">
    <div class="publish-card">
      <div class="publish-header">
        <h2>发布图书</h2>
        <p>分享您的好书，让知识传递</p>
      </div>

      <el-form
        ref="publishFormRef"
        :model="publishForm"
        :rules="publishRules"
        label-width="100px"
        class="publish-form"
      >
        <el-form-item label="书名" prop="title">
          <el-input
            v-model="publishForm.title"
            placeholder="请输入书名"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="作者" prop="author">
          <el-input
            v-model="publishForm.author"
            placeholder="请输入作者"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="分类" prop="category">
          <el-select
            v-model="publishForm.category"
            placeholder="请选择图书分类"
            style="width: 100%"
          >
            <el-option
              v-for="category in bookStore.categories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="成色" prop="condition">
          <el-radio-group v-model="publishForm.condition">
            <el-radio label="new">全新</el-radio>
            <el-radio label="good">良好</el-radio>
            <el-radio label="fair">一般</el-radio>
            <el-radio label="poor">较差</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="售价" prop="price">
          <el-input-number
            v-model="publishForm.price"
            :min="0.01"
            :max="9999.99"
            :precision="2"
            :step="0.1"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #666;">元</span>
        </el-form-item>

        <el-form-item label="封面图片" prop="cover">
          <el-input
            v-model="publishForm.cover"
            placeholder="请输入图片URL"
          />
          <div v-if="publishForm.cover" class="cover-preview">
            <img :src="publishForm.cover" alt="封面预览" @error="handleImageError" />
          </div>
        </el-form-item>

        <el-form-item label="图书简介" prop="description">
          <el-input
            v-model="publishForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入图书简介"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="bookStore.isLoading"
            @click="handlePublish"
          >
            发布图书
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="$router.back()">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElForm, ElMessage } from 'element-plus'
import { useBookStore } from '@/stores'
import type { PublishBookForm } from '@/types'

const router = useRouter()
const bookStore = useBookStore()

// 表单引用
const publishFormRef = ref<InstanceType<typeof ElForm>>()

// 发布表单数据
const publishForm = reactive<PublishBookForm>({
  title: '',
  author: '',
  description: '',
  price: 0,
  cover: '',
  condition: 'good',
  category: ''
})

// 表单验证规则
const publishRules = {
  title: [
    { required: true, message: '请输入书名', trigger: 'blur' },
    { min: 1, max: 100, message: '书名长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  author: [
    { required: true, message: '请输入作者', trigger: 'blur' },
    { min: 1, max: 50, message: '作者长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择图书分类', trigger: 'change' }
  ],
  condition: [
    { required: true, message: '请选择图书成色', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入售价', trigger: 'blur' },
    { type: 'number', min: 0.01, max: 9999.99, message: '售价必须在 0.01 到 9999.99 之间', trigger: 'blur' }
  ],
  cover: [
    { required: true, message: '请输入封面图片URL', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入图书简介', trigger: 'blur' },
    { min: 10, max: 500, message: '简介长度在 10 到 500 个字符', trigger: 'blur' }
  ]
}

// 处理图片加载错误
const handleImageError = () => {
  ElMessage.warning('图片加载失败，请检查URL是否正确')
}

// 处理发布
const handlePublish = async () => {
  if (!publishFormRef.value) return

  try {
    await publishFormRef.value.validate()
    await bookStore.publishBook(publishForm)
    
    ElMessage.success('图书发布成功！')
    router.push('/my-books')
  } catch (error) {
    console.error('发布失败:', error)
  }
}

// 重置表单
const handleReset = () => {
  if (publishFormRef.value) {
    publishFormRef.value.resetFields()
  }
}
</script>

<style scoped>
.publish-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 40px 20px;
}

.publish-card {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.publish-header {
  text-align: center;
  margin-bottom: 40px;
}

.publish-header h2 {
  color: #333;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.publish-header p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.publish-form {
  max-width: 600px;
  margin: 0 auto;
}

.cover-preview {
  margin-top: 10px;
  text-align: center;
}

.cover-preview img {
  max-width: 200px;
  max-height: 250px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .publish-card {
    padding: 30px 20px;
  }
  
  .publish-header h2 {
    font-size: 20px;
  }
  
  .publish-form :deep(.el-form-item__label) {
    width: 80px !important;
  }
}
</style>
