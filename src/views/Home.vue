<template>
  <div class="home-container">
    <!-- 搜索栏 -->
    <div class="search-section">
      <div class="search-content">
        <h1 class="page-title">校园二手书交易平台</h1>
        <p class="page-subtitle">发现好书，分享知识</p>
        
        <div class="search-bar">
          <el-input
            v-model="bookStore.searchKeyword"
            placeholder="搜索书名、作者..."
            size="large"
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select
            v-model="bookStore.selectedCategory"
            placeholder="选择分类"
            size="large"
            class="category-select"
            clearable
          >
            <el-option
              v-for="category in bookStore.categories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
          
          <el-button
            type="primary"
            size="large"
            class="search-btn"
            @click="handleSearch"
          >
            搜索
          </el-button>
        </div>
      </div>
    </div>

    <!-- 图书列表 -->
    <div class="books-section">
      <div class="books-header">
        <h2>最新图书</h2>
        <div class="books-actions">
          <el-button
            v-if="userStore.isLoggedIn"
            type="primary"
            @click="$router.push('/publish')"
          >
            发布图书
          </el-button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="bookStore.isLoading" class="loading-container">
        <el-skeleton :rows="6" animated />
      </div>

      <!-- 图书网格 -->
      <div v-else-if="bookStore.books.length > 0" class="books-grid">
        <div
          v-for="book in bookStore.books"
          :key="book.id"
          class="book-card"
          @click="goToBookDetail(book.id)"
        >
          <div class="book-cover">
            <img :src="book.cover" :alt="book.title" />
            <div v-if="book.status === 'sold'" class="sold-overlay">
              <span>已售出</span>
            </div>
          </div>
          
          <div class="book-info">
            <h3 class="book-title">{{ book.title }}</h3>
            <p class="book-author">{{ book.author }}</p>
            <p class="book-description">{{ book.description }}</p>
            
            <div class="book-meta">
              <span class="book-category">{{ book.category }}</span>
              <span class="book-condition">{{ getConditionText(book.condition) }}</span>
            </div>
            
            <div class="book-footer">
              <div class="book-price">¥{{ book.price }}</div>
              <div class="book-seller">{{ book.sellerName }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-empty description="暂无图书">
          <el-button
            v-if="userStore.isLoggedIn"
            type="primary"
            @click="$router.push('/publish')"
          >
            发布第一本图书
          </el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'
import { useUserStore, useBookStore } from '@/stores'

const router = useRouter()
const userStore = useUserStore()
const bookStore = useBookStore()

// 获取状态文本
const getConditionText = (condition: string) => {
  const conditionMap: Record<string, string> = {
    new: '全新',
    good: '良好',
    fair: '一般',
    poor: '较差'
  }
  return conditionMap[condition] || condition
}

// 搜索图书
const handleSearch = async () => {
  await bookStore.searchBooks()
}

// 跳转到图书详情
const goToBookDetail = (bookId: number) => {
  router.push(`/book/${bookId}`)
}

// 页面加载时获取图书列表
onMounted(async () => {
  await bookStore.getBooks()
})
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.search-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60px 20px;
  color: white;
}

.search-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
}

.search-bar {
  display: flex;
  gap: 15px;
  max-width: 800px;
  margin: 0 auto;
  flex-wrap: wrap;
  justify-content: center;
}

.search-input {
  flex: 1;
  min-width: 300px;
}

.category-select {
  width: 150px;
}

.search-btn {
  min-width: 100px;
}

.books-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.books-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.books-header h2 {
  font-size: 1.8rem;
  color: #333;
  margin: 0;
}

.loading-container {
  padding: 20px;
}

.books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.book-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.book-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.book-cover {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.book-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.sold-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
}

.book-info {
  padding: 20px;
}

.book-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.book-author {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 10px 0;
}

.book-description {
  color: #888;
  font-size: 0.85rem;
  line-height: 1.4;
  margin: 0 0 15px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.book-meta {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.book-category,
.book-condition {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.book-category {
  background: #e3f2fd;
  color: #1976d2;
}

.book-condition {
  background: #f3e5f5;
  color: #7b1fa2;
}

.book-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.book-price {
  font-size: 1.2rem;
  font-weight: 700;
  color: #e53e3e;
}

.book-seller {
  color: #666;
  font-size: 0.85rem;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .search-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input,
  .category-select,
  .search-btn {
    width: 100%;
  }

  .books-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .books-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
  }
}
</style>
