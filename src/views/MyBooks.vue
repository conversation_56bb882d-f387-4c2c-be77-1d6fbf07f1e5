<template>
  <div class="my-books-container">
    <div class="page-header">
      <h1>我的发布</h1>
      <el-button type="primary" @click="$router.push('/publish')">
        发布新图书
      </el-button>
    </div>

    <div v-if="bookStore.isLoading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>

    <div v-else-if="bookStore.myBooks.length > 0" class="books-list">
      <div
        v-for="book in bookStore.myBooks"
        :key="book.id"
        class="book-item"
      >
        <div class="book-cover">
          <img :src="book.cover" :alt="book.title" />
          <div v-if="book.status === 'sold'" class="status-badge sold">
            已售出
          </div>
          <div v-else class="status-badge available">
            在售
          </div>
        </div>

        <div class="book-info">
          <h3 class="book-title">{{ book.title }}</h3>
          <p class="book-author">{{ book.author }}</p>
          <p class="book-description">{{ book.description }}</p>
          
          <div class="book-meta">
            <el-tag size="small">{{ book.category }}</el-tag>
            <el-tag size="small" :type="getConditionType(book.condition)">
              {{ getConditionText(book.condition) }}
            </el-tag>
          </div>

          <div class="book-stats">
            <span class="publish-time">
              发布时间：{{ formatDate(book.publishedAt) }}
            </span>
            <span v-if="book.status === 'sold'" class="sold-time">
              售出时间：{{ formatDate(book.updatedAt) }}
            </span>
          </div>
        </div>

        <div class="book-actions">
          <div class="book-price">¥{{ book.price }}</div>
          <div class="action-buttons">
            <el-button
              size="small"
              @click="viewBook(book.id)"
            >
              查看详情
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="empty-state">
      <el-empty description="您还没有发布任何图书">
        <el-button type="primary" @click="$router.push('/publish')">
          发布第一本图书
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBookStore } from '@/stores'

const router = useRouter()
const bookStore = useBookStore()

// 获取状态文本和类型
const getConditionText = (condition: string) => {
  const conditionMap: Record<string, string> = {
    new: '全新',
    good: '良好',
    fair: '一般',
    poor: '较差'
  }
  return conditionMap[condition] || condition
}

const getConditionType = (condition: string) => {
  const typeMap: Record<string, string> = {
    new: 'success',
    good: 'primary',
    fair: 'warning',
    poor: 'danger'
  }
  return typeMap[condition] || 'info'
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 查看图书详情
const viewBook = (bookId: number) => {
  router.push(`/book/${bookId}`)
}

// 页面加载时获取我的图书
onMounted(async () => {
  await bookStore.getMyBooks()
})
</script>

<style scoped>
.my-books-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 40px 20px;
}

.page-header {
  max-width: 1200px;
  margin: 0 auto 30px auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h1 {
  font-size: 2rem;
  color: #333;
  margin: 0;
}

.loading-container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 30px;
}

.books-list {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.book-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: 120px 1fr auto;
  gap: 20px;
  align-items: start;
}

.book-cover {
  position: relative;
}

.book-cover img {
  width: 120px;
  height: 150px;
  object-fit: cover;
  border-radius: 8px;
}

.status-badge {
  position: absolute;
  top: 5px;
  right: 5px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.status-badge.available {
  background: #67c23a;
}

.status-badge.sold {
  background: #909399;
}

.book-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.book-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.4;
}

.book-author {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.book-description {
  color: #888;
  font-size: 0.85rem;
  line-height: 1.4;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.book-meta {
  display: flex;
  gap: 8px;
}

.book-stats {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 0.8rem;
  color: #999;
}

.book-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 15px;
}

.book-price {
  font-size: 1.3rem;
  font-weight: 700;
  color: #e53e3e;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.empty-state {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 60px 20px;
  text-align: center;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .page-header h1 {
    font-size: 1.5rem;
    text-align: center;
  }
  
  .book-item {
    grid-template-columns: 1fr;
    gap: 15px;
    text-align: center;
  }
  
  .book-cover {
    justify-self: center;
  }
  
  .book-actions {
    align-items: center;
  }
}
</style>
