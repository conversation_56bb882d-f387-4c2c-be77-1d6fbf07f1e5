<template>
  <div class="my-orders-container">
    <div class="page-header">
      <h1>我的订单</h1>
    </div>

    <div v-if="orderStore.isLoading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>

    <div v-else-if="orderStore.orders.length > 0" class="orders-list">
      <div
        v-for="order in orderStore.orders"
        :key="order.id"
        class="order-item"
      >
        <div class="order-header">
          <div class="order-info">
            <span class="order-id">订单号：{{ order.id }}</span>
            <span class="order-date">{{ formatDate(order.createdAt) }}</span>
          </div>
          <div class="order-status">
            <el-tag :type="getStatusType(order.status)">
              {{ getStatusText(order.status) }}
            </el-tag>
          </div>
        </div>

        <div class="order-content">
          <div class="book-info">
            <div class="book-cover">
              <img :src="order.book.cover" :alt="order.book.title" />
            </div>
            
            <div class="book-details">
              <h3 class="book-title">{{ order.book.title }}</h3>
              <p class="book-author">{{ order.book.author }}</p>
              <div class="book-meta">
                <el-tag size="small">{{ order.book.category }}</el-tag>
                <el-tag size="small" :type="getConditionType(order.book.condition)">
                  {{ getConditionText(order.book.condition) }}
                </el-tag>
              </div>
              <p class="seller-info">卖家：{{ order.book.sellerName }}</p>
            </div>
          </div>

          <div class="order-summary">
            <div class="price-info">
              <span class="price-label">实付金额：</span>
              <span class="price-value">¥{{ order.totalPrice }}</span>
            </div>
            
            <div class="order-actions">
              <el-button
                size="small"
                @click="viewBook(order.book.id)"
              >
                查看图书
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="empty-state">
      <el-empty description="您还没有任何订单">
        <el-button type="primary" @click="$router.push('/home')">
          去逛逛
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useOrderStore } from '@/stores'

const router = useRouter()
const orderStore = useOrderStore()

// 获取状态文本和类型
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待确认',
    confirmed: '已确认',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    confirmed: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return typeMap[status] || 'info'
}

const getConditionText = (condition: string) => {
  const conditionMap: Record<string, string> = {
    new: '全新',
    good: '良好',
    fair: '一般',
    poor: '较差'
  }
  return conditionMap[condition] || condition
}

const getConditionType = (condition: string) => {
  const typeMap: Record<string, string> = {
    new: 'success',
    good: 'primary',
    fair: 'warning',
    poor: 'danger'
  }
  return typeMap[condition] || 'info'
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 查看图书详情
const viewBook = (bookId: number) => {
  router.push(`/book/${bookId}`)
}

// 页面加载时获取订单列表
onMounted(async () => {
  await orderStore.getMyOrders()
})
</script>

<style scoped>
.my-orders-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 40px 20px;
}

.page-header {
  max-width: 1200px;
  margin: 0 auto 30px auto;
}

.page-header h1 {
  font-size: 2rem;
  color: #333;
  margin: 0;
}

.loading-container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 30px;
}

.orders-list {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.order-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.order-info {
  display: flex;
  gap: 20px;
  font-size: 0.9rem;
  color: #666;
}

.order-content {
  padding: 20px;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 20px;
  align-items: start;
}

.book-info {
  display: flex;
  gap: 15px;
}

.book-cover img {
  width: 80px;
  height: 100px;
  object-fit: cover;
  border-radius: 6px;
}

.book-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.book-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.4;
}

.book-author {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.book-meta {
  display: flex;
  gap: 8px;
}

.seller-info {
  color: #888;
  font-size: 0.85rem;
  margin: 0;
}

.order-summary {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 15px;
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.price-label {
  font-size: 0.9rem;
  color: #666;
}

.price-value {
  font-size: 1.3rem;
  font-weight: 700;
  color: #e53e3e;
}

.order-actions {
  display: flex;
  gap: 10px;
}

.empty-state {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 60px 20px;
  text-align: center;
}

@media (max-width: 768px) {
  .page-header h1 {
    font-size: 1.5rem;
    text-align: center;
  }
  
  .order-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .order-info {
    justify-content: space-between;
  }
  
  .order-content {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .order-summary {
    align-items: stretch;
    text-align: center;
  }
  
  .price-info {
    justify-content: center;
  }
}
</style>
