<template>
  <div class="book-detail-container">
    <div v-if="bookStore.isLoading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <div v-else-if="bookStore.currentBook" class="book-detail-content">
      <div class="book-detail-card">
        <div class="book-cover-section">
          <img :src="bookStore.currentBook.cover" :alt="bookStore.currentBook.title" />
          <div v-if="bookStore.currentBook.status === 'sold'" class="sold-badge">
            已售出
          </div>
        </div>

        <div class="book-info-section">
          <h1 class="book-title">{{ bookStore.currentBook.title }}</h1>
          <p class="book-author">作者：{{ bookStore.currentBook.author }}</p>
          
          <div class="book-meta">
            <el-tag type="primary">{{ bookStore.currentBook.category }}</el-tag>
            <el-tag :type="getConditionType(bookStore.currentBook.condition)">
              {{ getConditionText(bookStore.currentBook.condition) }}
            </el-tag>
          </div>

          <div class="book-price">
            <span class="price-label">售价：</span>
            <span class="price-value">¥{{ bookStore.currentBook.price }}</span>
          </div>

          <div class="book-seller">
            <span>卖家：{{ bookStore.currentBook.sellerName }}</span>
          </div>

          <div class="book-actions">
            <el-button
              v-if="canBuy"
              type="primary"
              size="large"
              :loading="bookStore.isLoading"
              @click="handleBuy"
            >
              立即购买
            </el-button>
            <el-button
              v-else-if="bookStore.currentBook.status === 'sold'"
              size="large"
              disabled
            >
              已售出
            </el-button>
            <el-button
              v-else-if="isMyBook"
              size="large"
              disabled
            >
              这是您发布的图书
            </el-button>
            <el-button
              v-else
              size="large"
              @click="$router.push('/login')"
            >
              登录后购买
            </el-button>
          </div>
        </div>
      </div>

      <div class="book-description-card">
        <h3>图书简介</h3>
        <p class="description-text">{{ bookStore.currentBook.description }}</p>
      </div>
    </div>

    <div v-else class="error-state">
      <el-result
        icon="warning"
        title="图书不存在"
        sub-title="您访问的图书可能已被删除或不存在"
      >
        <template #extra>
          <el-button type="primary" @click="$router.push('/home')">
            返回首页
          </el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore, useBookStore, useOrderStore } from '@/stores'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const bookStore = useBookStore()
const orderStore = useOrderStore()

// 计算属性
const canBuy = computed(() => {
  const book = bookStore.currentBook
  return book && 
         userStore.isLoggedIn && 
         book.status === 'available' && 
         book.sellerId !== userStore.user?.id
})

const isMyBook = computed(() => {
  const book = bookStore.currentBook
  return book && userStore.user && book.sellerId === userStore.user.id
})

// 获取状态文本和类型
const getConditionText = (condition: string) => {
  const conditionMap: Record<string, string> = {
    new: '全新',
    good: '良好',
    fair: '一般',
    poor: '较差'
  }
  return conditionMap[condition] || condition
}

const getConditionType = (condition: string) => {
  const typeMap: Record<string, string> = {
    new: 'success',
    good: 'primary',
    fair: 'warning',
    poor: 'danger'
  }
  return typeMap[condition] || 'info'
}

// 处理购买
const handleBuy = async () => {
  if (!bookStore.currentBook) return

  try {
    await ElMessageBox.confirm(
      `确认购买《${bookStore.currentBook.title}》？`,
      '确认购买',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const order = await bookStore.buyBook(bookStore.currentBook.id)
    orderStore.addOrder(order)
    
    ElMessage.success('购买成功！')
    router.push('/my-orders')
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('购买失败:', error)
    }
  }
}

// 页面加载时获取图书详情
onMounted(async () => {
  const bookId = parseInt(route.params.id as string)
  if (bookId) {
    try {
      await bookStore.getBookDetail(bookId)
    } catch (error) {
      console.error('获取图书详情失败:', error)
    }
  }
})
</script>

<style scoped>
.book-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 40px 20px;
}

.loading-container {
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 40px;
}

.book-detail-content {
  max-width: 1000px;
  margin: 0 auto;
}

.book-detail-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 40px;
}

.book-cover-section {
  position: relative;
}

.book-cover-section img {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sold-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
}

.book-info-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.book-title {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.book-author {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
}

.book-meta {
  display: flex;
  gap: 10px;
}

.book-price {
  display: flex;
  align-items: baseline;
  gap: 10px;
}

.price-label {
  font-size: 1.1rem;
  color: #666;
}

.price-value {
  font-size: 2rem;
  font-weight: 700;
  color: #e53e3e;
}

.book-seller {
  color: #666;
  font-size: 1rem;
}

.book-actions {
  margin-top: auto;
}

.book-description-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.book-description-card h3 {
  font-size: 1.3rem;
  color: #333;
  margin: 0 0 15px 0;
}

.description-text {
  color: #666;
  line-height: 1.6;
  font-size: 1rem;
  margin: 0;
}

.error-state {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
}

@media (max-width: 768px) {
  .book-detail-card {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 20px;
  }
  
  .book-title {
    font-size: 1.5rem;
  }
  
  .price-value {
    font-size: 1.5rem;
  }
}
</style>
