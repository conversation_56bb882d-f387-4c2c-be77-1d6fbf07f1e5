import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Book, PublishBookForm } from '@/types'
import { bookApi } from '@/api'
import { ElMessage } from 'element-plus'

export const useBookStore = defineStore('book', () => {
  // 状态
  const books = ref<Book[]>([])
  const myBooks = ref<Book[]>([])
  const currentBook = ref<Book | null>(null)
  const isLoading = ref(false)
  const searchKeyword = ref('')
  const selectedCategory = ref('')

  // 图书分类
  const categories = ref([
    '计算机科学',
    '编程语言',
    '前端开发',
    '后端开发',
    '数据库',
    '算法与数据结构',
    '软件工程',
    '人工智能',
    '机器学习',
    '网络安全',
    '其他'
  ])

  // 获取图书列表
  const getBooks = async (params?: { category?: string; keyword?: string }) => {
    try {
      isLoading.value = true
      const response = await bookApi.getBooks(params)
      books.value = response
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '获取图书列表失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 搜索图书
  const searchBooks = async () => {
    const params: any = {}
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value
    }
    if (selectedCategory.value) {
      params.category = selectedCategory.value
    }
    return await getBooks(params)
  }

  // 获取图书详情
  const getBookDetail = async (id: number) => {
    try {
      isLoading.value = true
      const response = await bookApi.getBookDetail(id)
      currentBook.value = response
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '获取图书详情失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 发布图书
  const publishBook = async (bookData: PublishBookForm) => {
    try {
      isLoading.value = true
      const response = await bookApi.publishBook(bookData)
      
      // 添加到图书列表
      books.value.unshift(response)
      myBooks.value.unshift(response)
      
      ElMessage.success('图书发布成功')
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '发布图书失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 购买图书
  const buyBook = async (bookId: number) => {
    try {
      isLoading.value = true
      const response = await bookApi.buyBook(bookId)
      
      // 更新图书状态
      const bookIndex = books.value.findIndex(book => book.id === bookId)
      if (bookIndex !== -1) {
        books.value[bookIndex].status = 'sold'
      }
      
      if (currentBook.value && currentBook.value.id === bookId) {
        currentBook.value.status = 'sold'
      }
      
      ElMessage.success('购买成功')
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '购买失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 获取我发布的图书
  const getMyBooks = async () => {
    try {
      isLoading.value = true
      const response = await bookApi.getMyBooks()
      myBooks.value = response
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '获取我的图书失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 清空搜索条件
  const clearSearch = () => {
    searchKeyword.value = ''
    selectedCategory.value = ''
  }

  return {
    // 状态
    books,
    myBooks,
    currentBook,
    isLoading,
    searchKeyword,
    selectedCategory,
    categories,
    
    // 方法
    getBooks,
    searchBooks,
    getBookDetail,
    publishBook,
    buyBook,
    getMyBooks,
    clearSearch
  }
})
