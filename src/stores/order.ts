import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Order } from '@/types'
import { orderApi } from '@/api'
import { ElMessage } from 'element-plus'

export const useOrderStore = defineStore('order', () => {
  // 状态
  const orders = ref<Order[]>([])
  const isLoading = ref(false)

  // 获取我的订单
  const getMyOrders = async () => {
    try {
      isLoading.value = true
      const response = await orderApi.getMyOrders()
      orders.value = response
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '获取订单列表失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 添加订单（购买成功后调用）
  const addOrder = (order: Order) => {
    orders.value.unshift(order)
  }

  return {
    // 状态
    orders,
    isLoading,
    
    // 方法
    getMyOrders,
    addOrder
  }
})
