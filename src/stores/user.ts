import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginForm, RegisterForm } from '@/types'
import { userApi } from '@/api'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  // 初始化用户信息（从localStorage恢复）
  const initUser = () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    
    if (savedToken && savedUser) {
      token.value = savedToken
      user.value = JSON.parse(savedUser)
    }
  }

  // 登录
  const login = async (loginForm: LoginForm) => {
    try {
      isLoading.value = true
      const response = await userApi.login(loginForm)
      
      // 保存用户信息和token
      user.value = response.user
      token.value = response.token
      
      // 持久化存储
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
      ElMessage.success('登录成功')
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (registerForm: RegisterForm) => {
    try {
      isLoading.value = true
      const response = await userApi.register(registerForm)
      
      // 注册成功后自动登录
      user.value = response.user
      token.value = response.token
      
      // 持久化存储
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
      ElMessage.success('注册成功')
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '注册失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = ''
    
    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    
    ElMessage.success('已退出登录')
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const userInfo = await userApi.getUserInfo()
      user.value = userInfo
      localStorage.setItem('user', JSON.stringify(userInfo))
      return userInfo
    } catch (error: any) {
      ElMessage.error(error.message || '获取用户信息失败')
      // 如果获取用户信息失败，可能是token过期，执行登出
      logout()
      throw error
    }
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    isAdmin,
    
    // 方法
    initUser,
    login,
    register,
    logout,
    getUserInfo
  }
})
