<script setup lang="ts">
import { onMounted } from 'vue'
import { useUserStore } from '@/stores'
import AppHeader from '@/components/Layout/AppHeader.vue'

const userStore = useUserStore()

// 应用初始化
onMounted(() => {
  // 从本地存储恢复用户信息
  userStore.initUser()
})
</script>

<template>
  <div id="app">
    <!-- 头部导航 -->
    <AppHeader />

    <!-- 主要内容区域 -->
    <main class="main-content">
      <router-view />
    </main>
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f7fa;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}

/* 全局链接样式 */
a {
  color: #409eff;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 响应式图片 */
img {
  max-width: 100%;
  height: auto;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
