<template>
  <header class="app-header">
    <div class="header-content">
      <!-- Logo和标题 -->
      <div class="header-left">
        <router-link to="/home" class="logo-link">
          <h1 class="logo">📚 校园二手书</h1>
        </router-link>
      </div>

      <!-- 导航菜单 -->
      <nav class="header-nav">
        <router-link to="/home" class="nav-link">首页</router-link>
        <router-link v-if="userStore.isLoggedIn" to="/publish" class="nav-link">
          发布图书
        </router-link>
      </nav>

      <!-- 用户操作区 -->
      <div class="header-right">
        <template v-if="userStore.isLoggedIn">
          <!-- 用户菜单 -->
          <el-dropdown @command="handleCommand">
            <div class="user-menu">
              <el-avatar :size="32" :src="userStore.user?.avatar" />
              <span class="username">{{ userStore.user?.username }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                <el-dropdown-item command="my-books">我的发布</el-dropdown-item>
                <el-dropdown-item command="my-orders">我的订单</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        
        <template v-else>
          <!-- 登录注册按钮 -->
          <div class="auth-buttons">
            <el-button @click="$router.push('/login')">登录</el-button>
            <el-button type="primary" @click="$router.push('/register')">
              注册
            </el-button>
          </div>
        </template>
      </div>

      <!-- 移动端菜单按钮 -->
      <div class="mobile-menu-btn">
        <el-button
          :icon="Menu"
          circle
          @click="showMobileMenu = true"
        />
      </div>
    </div>

    <!-- 移动端抽屉菜单 -->
    <el-drawer
      v-model="showMobileMenu"
      title="菜单"
      direction="rtl"
      size="280px"
    >
      <div class="mobile-menu">
        <div v-if="userStore.isLoggedIn" class="mobile-user-info">
          <el-avatar :size="50" :src="userStore.user?.avatar" />
          <div class="user-details">
            <div class="username">{{ userStore.user?.username }}</div>
            <div class="user-email">{{ userStore.user?.email }}</div>
          </div>
        </div>

        <div class="mobile-nav">
          <router-link
            to="/home"
            class="mobile-nav-item"
            @click="showMobileMenu = false"
          >
            首页
          </router-link>
          
          <template v-if="userStore.isLoggedIn">
            <router-link
              to="/publish"
              class="mobile-nav-item"
              @click="showMobileMenu = false"
            >
              发布图书
            </router-link>
            <router-link
              to="/my-books"
              class="mobile-nav-item"
              @click="showMobileMenu = false"
            >
              我的发布
            </router-link>
            <router-link
              to="/my-orders"
              class="mobile-nav-item"
              @click="showMobileMenu = false"
            >
              我的订单
            </router-link>
            <router-link
              to="/profile"
              class="mobile-nav-item"
              @click="showMobileMenu = false"
            >
              个人中心
            </router-link>
            <div class="mobile-nav-item logout" @click="handleLogout">
              退出登录
            </div>
          </template>
          
          <template v-else>
            <router-link
              to="/login"
              class="mobile-nav-item"
              @click="showMobileMenu = false"
            >
              登录
            </router-link>
            <router-link
              to="/register"
              class="mobile-nav-item"
              @click="showMobileMenu = false"
            >
              注册
            </router-link>
          </template>
        </div>
      </div>
    </el-drawer>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowDown, Menu } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores'

const router = useRouter()
const userStore = useUserStore()

// 移动端菜单显示状态
const showMobileMenu = ref(false)

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'my-books':
      router.push('/my-books')
      break
    case 'my-orders':
      router.push('/my-orders')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确认退出登录吗？',
      '退出登录',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    userStore.logout()
    showMobileMenu.value = false
    router.push('/home')
  } catch (error) {
    // 用户取消
  }
}
</script>

<style scoped>
.app-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  flex-shrink: 0;
}

.logo-link {
  text-decoration: none;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: #409eff;
  margin: 0;
}

.header-nav {
  display: flex;
  gap: 30px;
  margin-left: 50px;
}

.nav-link {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: #409eff;
}

.header-right {
  flex-shrink: 0;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-menu:hover {
  background-color: #f5f7fa;
}

.username {
  font-weight: 500;
  color: #333;
}

.auth-buttons {
  display: flex;
  gap: 10px;
}

.mobile-menu-btn {
  display: none;
}

.mobile-menu {
  padding: 20px 0;
}

.mobile-user-info {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 0 20px 20px 20px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.user-details {
  flex: 1;
}

.user-details .username {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.user-email {
  font-size: 0.85rem;
  color: #666;
}

.mobile-nav {
  display: flex;
  flex-direction: column;
}

.mobile-nav-item {
  display: block;
  padding: 15px 20px;
  color: #333;
  text-decoration: none;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s;
}

.mobile-nav-item:hover,
.mobile-nav-item.router-link-active {
  background-color: #f5f7fa;
  color: #409eff;
}

.mobile-nav-item.logout {
  color: #f56c6c;
  cursor: pointer;
}

@media (max-width: 768px) {
  .header-nav,
  .header-right {
    display: none;
  }
  
  .mobile-menu-btn {
    display: block;
  }
  
  .logo {
    font-size: 1.2rem;
  }
}
</style>
