import type { User, Book, Order } from '@/types'

// 模拟用户数据
export const mockUsers: User[] = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    username: 'user1',
    email: '<EMAIL>',
    role: 'user',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    createdAt: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    username: 'user2',
    email: '<EMAIL>',
    role: 'user',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    createdAt: '2024-01-03T00:00:00Z'
  }
]

// 模拟图书数据
export const mockBooks: Book[] = [
  {
    id: 1,
    title: '算法导论',
    author: '<PERSON>. Cormen',
    description: '计算机科学经典教材，深入浅出地介绍了算法设计与分析的基本方法。',
    price: 89.9,
    cover: 'https://img3.doubanio.com/view/subject_l/public/s1959967.jpg',
    condition: 'good',
    category: '计算机科学',
    sellerId: 2,
    sellerName: 'user1',
    status: 'available',
    publishedAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-10T10:00:00Z'
  },
  {
    id: 2,
    title: 'JavaScript高级程序设计',
    author: 'Nicholas C. Zakas',
    description: 'JavaScript开发者必读经典，全面介绍JavaScript语言特性和最佳实践。',
    price: 65.5,
    cover: 'https://img9.doubanio.com/view/subject_l/public/s8958650.jpg',
    condition: 'new',
    category: '编程语言',
    sellerId: 3,
    sellerName: 'user2',
    status: 'available',
    publishedAt: '2024-01-11T14:30:00Z',
    updatedAt: '2024-01-11T14:30:00Z'
  },
  {
    id: 3,
    title: '深入理解计算机系统',
    author: 'Randal E. Bryant',
    description: '从程序员的角度深入理解计算机系统，是系统编程的经典教材。',
    price: 78.0,
    cover: 'https://img3.doubanio.com/view/subject_l/public/s4510534.jpg',
    condition: 'good',
    category: '计算机科学',
    sellerId: 2,
    sellerName: 'user1',
    status: 'sold',
    publishedAt: '2024-01-09T09:15:00Z',
    updatedAt: '2024-01-12T16:20:00Z'
  },
  {
    id: 4,
    title: 'Vue.js设计与实现',
    author: '霍春阳',
    description: 'Vue.js框架核心原理深度解析，适合想要深入理解Vue.js的开发者。',
    price: 55.8,
    cover: 'https://img2.doubanio.com/view/subject_l/public/s34062878.jpg',
    condition: 'new',
    category: '前端开发',
    sellerId: 3,
    sellerName: 'user2',
    status: 'available',
    publishedAt: '2024-01-12T11:45:00Z',
    updatedAt: '2024-01-12T11:45:00Z'
  }
]

// 模拟订单数据
export const mockOrders: Order[] = [
  {
    id: 1,
    bookId: 3,
    buyerId: 3,
    sellerId: 2,
    book: mockBooks[2],
    status: 'completed',
    totalPrice: 78.0,
    createdAt: '2024-01-12T16:00:00Z',
    updatedAt: '2024-01-12T16:20:00Z'
  }
]
