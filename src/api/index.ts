import request from '@/utils/request'
import type { User, Book, Order, LoginForm, RegisterForm, PublishBookForm } from '@/types'
import { mockUsers, mockBooks, mockOrders } from '@/mock/data'

// 模拟延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 用户相关API
export const userApi = {
  // 登录
  async login(data: LoginForm): Promise<{ user: User; token: string }> {
    await delay(1000)
    
    const user = mockUsers.find(u => u.username === data.username)
    if (!user) {
      throw new Error('用户名或密码错误')
    }
    
    // 简单的密码验证（实际项目中应该在后端验证）
    if (data.password !== '123456') {
      throw new Error('用户名或密码错误')
    }
    
    return {
      user,
      token: `mock-token-${user.id}-${Date.now()}`
    }
  },

  // 注册
  async register(data: RegisterForm): Promise<{ user: User; token: string }> {
    await delay(1000)
    
    // 检查用户名是否已存在
    const existingUser = mockUsers.find(u => u.username === data.username)
    if (existingUser) {
      throw new Error('用户名已存在')
    }
    
    // 检查邮箱是否已存在
    const existingEmail = mockUsers.find(u => u.email === data.email)
    if (existingEmail) {
      throw new Error('邮箱已被注册')
    }
    
    const newUser: User = {
      id: mockUsers.length + 1,
      username: data.username,
      email: data.email,
      role: 'user',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      createdAt: new Date().toISOString()
    }
    
    mockUsers.push(newUser)
    
    return {
      user: newUser,
      token: `mock-token-${newUser.id}-${Date.now()}`
    }
  },

  // 获取用户信息
  async getUserInfo(): Promise<User> {
    await delay(500)
    // 从token中解析用户ID（实际项目中应该在后端验证token）
    const token = localStorage.getItem('token')
    if (!token) {
      throw new Error('未登录')
    }
    
    const userId = parseInt(token.split('-')[2])
    const user = mockUsers.find(u => u.id === userId)
    if (!user) {
      throw new Error('用户不存在')
    }
    
    return user
  }
}

// 图书相关API
export const bookApi = {
  // 获取图书列表
  async getBooks(params?: { category?: string; keyword?: string }): Promise<Book[]> {
    await delay(800)
    
    let books = [...mockBooks]
    
    // 按分类筛选
    if (params?.category) {
      books = books.filter(book => book.category === params.category)
    }
    
    // 按关键词搜索
    if (params?.keyword) {
      const keyword = params.keyword.toLowerCase()
      books = books.filter(book => 
        book.title.toLowerCase().includes(keyword) ||
        book.author.toLowerCase().includes(keyword) ||
        book.description.toLowerCase().includes(keyword)
      )
    }
    
    return books
  },

  // 获取图书详情
  async getBookDetail(id: number): Promise<Book> {
    await delay(500)
    
    const book = mockBooks.find(b => b.id === id)
    if (!book) {
      throw new Error('图书不存在')
    }
    
    return book
  },

  // 发布图书
  async publishBook(data: PublishBookForm): Promise<Book> {
    await delay(1000)
    
    const user = JSON.parse(localStorage.getItem('user') || '{}')
    if (!user.id) {
      throw new Error('请先登录')
    }
    
    const newBook: Book = {
      id: mockBooks.length + 1,
      ...data,
      sellerId: user.id,
      sellerName: user.username,
      status: 'available',
      publishedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    mockBooks.push(newBook)
    return newBook
  },

  // 购买图书
  async buyBook(bookId: number): Promise<Order> {
    await delay(1000)
    
    const user = JSON.parse(localStorage.getItem('user') || '{}')
    if (!user.id) {
      throw new Error('请先登录')
    }
    
    const book = mockBooks.find(b => b.id === bookId)
    if (!book) {
      throw new Error('图书不存在')
    }
    
    if (book.status !== 'available') {
      throw new Error('图书已售出')
    }
    
    if (book.sellerId === user.id) {
      throw new Error('不能购买自己发布的图书')
    }
    
    // 更新图书状态
    book.status = 'sold'
    book.updatedAt = new Date().toISOString()
    
    // 创建订单
    const newOrder: Order = {
      id: mockOrders.length + 1,
      bookId: book.id,
      buyerId: user.id,
      sellerId: book.sellerId,
      book,
      status: 'completed',
      totalPrice: book.price,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    mockOrders.push(newOrder)
    return newOrder
  },

  // 获取我发布的图书
  async getMyBooks(): Promise<Book[]> {
    await delay(500)
    
    const user = JSON.parse(localStorage.getItem('user') || '{}')
    if (!user.id) {
      throw new Error('请先登录')
    }
    
    return mockBooks.filter(book => book.sellerId === user.id)
  }
}

// 订单相关API
export const orderApi = {
  // 获取我的订单
  async getMyOrders(): Promise<Order[]> {
    await delay(500)
    
    const user = JSON.parse(localStorage.getItem('user') || '{}')
    if (!user.id) {
      throw new Error('请先登录')
    }
    
    return mockOrders.filter(order => order.buyerId === user.id)
  }
}
