// 用户类型
export interface User {
  id: number
  username: string
  email: string
  role: 'user' | 'admin'
  avatar?: string
  createdAt: string
}

// 图书类型
export interface Book {
  id: number
  title: string
  author: string
  description: string
  price: number
  cover: string
  condition: 'new' | 'good' | 'fair' | 'poor'
  category: string
  sellerId: number
  sellerName: string
  status: 'available' | 'sold' | 'reserved'
  publishedAt: string
  updatedAt: string
}

// 订单类型
export interface Order {
  id: number
  bookId: number
  buyerId: number
  sellerId: number
  book: Book
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled'
  totalPrice: number
  createdAt: string
  updatedAt: string
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 登录表单类型
export interface LoginForm {
  username: string
  password: string
}

// 注册表单类型
export interface RegisterForm {
  username: string
  email: string
  password: string
  confirmPassword: string
}

// 发布图书表单类型
export interface PublishBookForm {
  title: string
  author: string
  description: string
  price: number
  cover: string
  condition: 'new' | 'good' | 'fair' | 'poor'
  category: string
}
