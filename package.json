{"name": "untitled1", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/node": "^22.15.29", "@vue/tsconfig": "^0.7.0", "axios": "^1.9.0", "bootstrap": "^5.3.6", "element-plus": "^2.9.11", "pinia": "^3.0.3", "typescript": "^5.8.3", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.10"}}